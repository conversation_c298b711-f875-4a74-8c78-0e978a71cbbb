# Template configuration for Playwright Script Generator
# Copy this file to config.yaml and fill in your values, or use environment variables

# OpenAI API Configuration
openai:
  # Common settings
  api_key: "" # Set this to your API key or use OPENAI_API_KEY environment variable
  model: "gpt-4o"
  max_tokens: 4000
  temperature: 0.2

  # Azure OpenAI specific settings
  use_azure: true # Set to false if using regular OpenAI instead of Azure
  azure_endpoint: "" # Set this to your Azure endpoint or use AZURE_OPENAI_ENDPOINT environment variable
  azure_api_version: "2023-05-15" # Azure OpenAI API version
  azure_deployment_name_vision: "gpt-4o" # Azure OpenAI deployment name for vision model
  azure_deployment_name_completion: "gpt-4o" # Azure OpenAI deployment name for completion model

# Playwright Configuration
playwright:
  timeout: 30000  # milliseconds
  viewport:
    width: 1280
    height: 720
  headless: true

# Script Generation
script:
  template_path: ""  # Optional path to custom script template
  add_comments: true
  validate_selectors: true
  include_assertions: true

# Output Settings
output:
  indent_size: 4
  force_overwrite: false

# Validation and correction system configuration
validation:
  enabled: true
  max_retries: 3
  timeout_per_execution: 60
  success_criteria:
    script_executes_without_errors: true
    expected_output_format: true
    minimum_results_count: 1
    no_timeout_errors: true
    no_assertion_errors: true
  save_intermediate_versions: true
  detailed_logging: true
